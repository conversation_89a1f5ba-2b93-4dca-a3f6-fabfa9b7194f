package com.wealthfront.lending.mortgages.application.model.data.factory;

import com.google.inject.Provider;
import com.kaching.platform.common.Option;
import com.wealthfront.auto.types.global.TestAddressData;
import com.wealthfront.auto.types.global.TestEntityAction;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class TestAddressDataFactory {
  public static TestAddressDataBuilder createAddressData(Provider<UUID> uuidProvider) {
    TestAddressDataBuilder builder = new TestAddressDataBuilder();
    builder.withId(uuidProvider.get().toString());
    builder.withAction(TestEntityAction.ADD);
    return builder;
  }

  public static TestAddressDataBuilder updateAddressData(String id) {
    TestAddressDataBuilder builder = new TestAddressDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.UPDATE);
    return builder;
  }

  public static TestAddressData deleteAddressData(String id) {
    TestAddressDataBuilder builder = new TestAddressDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.REMOVE);
    return builder.build();
  }

  public static class TestAddressDataBuilder {
    private TestEntityAction action;

    private String id;

    private Option<String> streetAddress;

    private Option<String> city;

    private Option<String> state;

    private Option<String> zipCode;

    TestAddressDataBuilder() {
    }

    private TestAddressDataBuilder withId(String id) {
      this.id = id;
      return this;
    }

    public TestAddressDataBuilder withAction(TestEntityAction action) {
      this.action = action;
      return this;
    }

    public TestAddressDataBuilder withStreetAddress(String streetAddress) {
      this.streetAddress = Option.of(streetAddress);
      return this;
    }

    public TestAddressDataBuilder withCity(String city) {
      this.city = Option.of(city);
      return this;
    }

    public TestAddressDataBuilder withState(String state) {
      this.state = Option.of(state);
      return this;
    }

    public TestAddressDataBuilder withZipCode(String zipCode) {
      this.zipCode = Option.of(zipCode);
      return this;
    }

    public TestAddressData build() {
      TestAddressData.Builder entityBuilder = new TestAddressData.Builder();
      entityBuilder.withId(this.id);
      entityBuilder.withAction(this.action);
      if (this.streetAddress != null) { entityBuilder.withStreetAddress(this.streetAddress.getOrNull()); };
      if (this.city != null) { entityBuilder.withCity(this.city.getOrNull()); };
      if (this.state != null) { entityBuilder.withState(this.state.getOrNull()); };
      if (this.zipCode != null) { entityBuilder.withZipCode(this.zipCode.getOrNull()); };
      TestAddressData entity = entityBuilder.build();
      entity.setFieldsToNullSet(getFieldsToNullSet());
      return entity;
    }

    private Set<String> getFieldsToNullSet() {
      Set<String> fieldsToNullSet = new HashSet<>();
      if (this.streetAddress != null && this.streetAddress.isEmpty()) { fieldsToNullSet.add("streetAddress"); };
      if (this.city != null && this.city.isEmpty()) { fieldsToNullSet.add("city"); };
      if (this.state != null && this.state.isEmpty()) { fieldsToNullSet.add("state"); };
      if (this.zipCode != null && this.zipCode.isEmpty()) { fieldsToNullSet.add("zipCode"); };
      return fieldsToNullSet;
    }
  }
}
