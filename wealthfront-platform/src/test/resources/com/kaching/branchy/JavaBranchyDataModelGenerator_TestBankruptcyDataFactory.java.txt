package com.wealthfront.lending.mortgages.application.model.data.factory;

import com.google.inject.Provider;
import com.kaching.platform.common.Option;
import com.wealthfront.auto.types.global.TestBankruptcyChapterType;
import com.wealthfront.auto.types.global.TestBankruptcyData;
import com.wealthfront.auto.types.global.TestEntityAction;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class TestBankruptcyDataFactory {
  public static TestBankruptcyDataBuilder createBankruptcyData(Provider<UUID> uuidProvider) {
    TestBankruptcyDataBuilder builder = new TestBankruptcyDataBuilder();
    builder.withId(uuidProvider.get().toString());
    builder.withAction(TestEntityAction.ADD);
    return builder;
  }

  public static TestBankruptcyDataBuilder updateBankruptcyData(String id) {
    TestBankruptcyDataBuilder builder = new TestBankruptcyDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.UPDATE);
    return builder;
  }

  public static TestBankruptcyData deleteBankruptcyData(String id) {
    TestBankruptcyDataBuilder builder = new TestBankruptcyDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.REMOVE);
    return builder.build();
  }

  public static class TestBankruptcyDataBuilder {
    private TestEntityAction action;

    private String id;

    private Option<TestBankruptcyChapterType> bankruptcyChapterType;

    TestBankruptcyDataBuilder() {
    }

    private TestBankruptcyDataBuilder withId(String id) {
      this.id = id;
      return this;
    }

    public TestBankruptcyDataBuilder withAction(TestEntityAction action) {
      this.action = action;
      return this;
    }

    public TestBankruptcyDataBuilder withBankruptcyChapterType(
        TestBankruptcyChapterType bankruptcyChapterType) {
      this.bankruptcyChapterType = Option.of(bankruptcyChapterType);
      return this;
    }

    public TestBankruptcyData build() {
      TestBankruptcyData.Builder entityBuilder = new TestBankruptcyData.Builder();
      entityBuilder.withId(this.id);
      entityBuilder.withAction(this.action);
      if (this.bankruptcyChapterType != null) { entityBuilder.withBankruptcyChapterType(this.bankruptcyChapterType.getOrNull()); };
      TestBankruptcyData entity = entityBuilder.build();
      entity.setFieldsToNullSet(getFieldsToNullSet());
      return entity;
    }

    private Set<String> getFieldsToNullSet() {
      Set<String> fieldsToNullSet = new HashSet<>();
      if (this.bankruptcyChapterType != null && this.bankruptcyChapterType.isEmpty()) { fieldsToNullSet.add("bankruptcyChapterType"); };
      return fieldsToNullSet;
    }
  }
}
