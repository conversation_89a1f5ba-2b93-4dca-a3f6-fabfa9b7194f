package com.wealthfront.lending.mortgages.application.model.data.factory;

import com.google.inject.Provider;
import com.kaching.platform.common.Option;
import com.wealthfront.auto.types.global.TestApplicationData;
import com.wealthfront.auto.types.global.TestBorrowerData;
import com.wealthfront.auto.types.global.TestCreditPullData;
import com.wealthfront.auto.types.global.TestEntityAction;
import com.wealthfront.auto.types.global.TestLoanAmountData;
import com.wealthfront.auto.types.global.TestMortgageType;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class TestApplicationDataFactory {
  public static TestApplicationDataBuilder createApplicationData(Provider<UUID> uuidProvider) {
    TestApplicationDataBuilder builder = new TestApplicationDataBuilder();
    builder.withId(uuidProvider.get().toString());
    builder.withAction(TestEntityAction.ADD);
    return builder;
  }

  public static TestApplicationDataBuilder updateApplicationData(String id) {
    TestApplicationDataBuilder builder = new TestApplicationDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.UPDATE);
    return builder;
  }

  public static TestApplicationData deleteApplicationData(String id) {
    TestApplicationDataBuilder builder = new TestApplicationDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.REMOVE);
    return builder.build();
  }

  public static class TestApplicationDataBuilder {
    private TestEntityAction action;

    private String id;

    private Option<TestMortgageType> mortgageType;

    private Option<List<TestBorrowerData>> borrowers;

    private Option<TestLoanAmountData> loanAmount;

    private Option<List<TestCreditPullData>> creditPulls;

    TestApplicationDataBuilder() {
    }

    private TestApplicationDataBuilder withId(String id) {
      this.id = id;
      return this;
    }

    public TestApplicationDataBuilder withAction(TestEntityAction action) {
      this.action = action;
      return this;
    }

    public TestApplicationDataBuilder withMortgageType(TestMortgageType mortgageType) {
      this.mortgageType = Option.of(mortgageType);
      return this;
    }

    public TestApplicationDataBuilder withBorrowers(List<TestBorrowerData> borrowers) {
      this.borrowers = Option.of(borrowers);
      return this;
    }

    public TestApplicationDataBuilder withLoanAmount(TestLoanAmountData loanAmount) {
      this.loanAmount = Option.of(loanAmount);
      return this;
    }

    public TestApplicationDataBuilder withCreditPulls(List<TestCreditPullData> creditPulls) {
      this.creditPulls = Option.of(creditPulls);
      return this;
    }

    public TestApplicationData build() {
      TestApplicationData.Builder entityBuilder = new TestApplicationData.Builder();
      entityBuilder.withId(this.id);
      entityBuilder.withAction(this.action);
      if (this.mortgageType != null) { entityBuilder.withMortgageType(this.mortgageType.getOrNull()); };
      if (this.borrowers != null) { entityBuilder.withBorrowers(this.borrowers.getOrNull()); };
      if (this.loanAmount != null) { entityBuilder.withLoanAmount(this.loanAmount.getOrNull()); };
      if (this.creditPulls != null) { entityBuilder.withCreditPulls(this.creditPulls.getOrNull()); };
      TestApplicationData entity = entityBuilder.build();
      entity.setFieldsToNullSet(getFieldsToNullSet());
      return entity;
    }

    private Set<String> getFieldsToNullSet() {
      Set<String> fieldsToNullSet = new HashSet<>();
      if (this.mortgageType != null && this.mortgageType.isEmpty()) { fieldsToNullSet.add("mortgageType"); };
      if (this.borrowers != null && this.borrowers.isEmpty()) { fieldsToNullSet.add("borrowers"); };
      if (this.loanAmount != null && this.loanAmount.isEmpty()) { fieldsToNullSet.add("loanAmount"); };
      if (this.creditPulls != null && this.creditPulls.isEmpty()) { fieldsToNullSet.add("creditPulls"); };
      return fieldsToNullSet;
    }
  }
} 