package com.wealthfront.lending.mortgages.application.model.data.factory;

import com.google.inject.Provider;
import com.kaching.entities.EmailAddress;
import com.kaching.entities.PhoneNumber;
import com.kaching.platform.common.Option;
import com.wealthfront.auto.types.global.TestAddressData;
import com.wealthfront.auto.types.global.TestBankruptcyData;
import com.wealthfront.auto.types.global.TestBorrowerData;
import com.wealthfront.auto.types.global.TestBorrowerType;
import com.wealthfront.auto.types.global.TestBorrowerWorkflowData;
import com.wealthfront.auto.types.global.TestCitizenshipType;
import com.wealthfront.auto.types.global.TestCreditPullConsentType;
import com.wealthfront.auto.types.global.TestCreditPullStatus;
import com.wealthfront.auto.types.global.TestCreditPullType;
import com.wealthfront.auto.types.global.TestCreditScoreData;
import com.wealthfront.auto.types.global.TestEmploymentStatus;
import com.wealthfront.auto.types.global.TestEntityAction;
import com.wealthfront.auto.types.global.TestIncomeWorkflowData;
import com.wealthfront.auto.types.global.TestMaritalStatus;
import com.wealthfront.auto.types.global.TestMilitaryStatus;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

public class TestBorrowerDataFactory {
  public static TestBorrowerDataBuilder createBorrowerData(Provider<UUID> uuidProvider) {
    TestBorrowerDataBuilder builder = new TestBorrowerDataBuilder();
    builder.withId(uuidProvider.get().toString());
    builder.withAction(TestEntityAction.ADD);
    return builder;
  }

  public static TestBorrowerDataBuilder updateBorrowerData(String id) {
    TestBorrowerDataBuilder builder = new TestBorrowerDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.UPDATE);
    return builder;
  }

  public static TestBorrowerData deleteBorrowerData(String id) {
    TestBorrowerDataBuilder builder = new TestBorrowerDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.REMOVE);
    return builder.build();
  }

  public static class TestBorrowerDataBuilder {
    private TestEntityAction action;

    private String id;

    private Option<TestIncomeWorkflowData> incomeWorkflowData;

    private Option<TestBorrowerType> borrowerType;

    private Option<String> firstName;

    private Option<String> middleName;

    private Option<String> lastName;

    private Option<String> suffix;

    private Option<List<String>> alternativeNames;

    private Option<DateTime> termsOfServiceConsentedAt;

    private Option<DateTime> creditPullConsentedAt;

    private Option<EmailAddress> emailAddress;

    private Option<PhoneNumber> phoneNumber;

    private Option<LocalDate> dateOfBirth;

    private Option<TestAddressData> currentAddress;

    private Option<TestAddressData> mailingAddress;

    private Option<TestMaritalStatus> maritalStatus;

    private Option<TestCitizenshipType> citizenshipType;

    private Option<String> spouseBorrowerId;

    private Option<Boolean> isCurrentlyLivingWithCoBorrowers;

    private Option<Boolean> isIntendingToOccupy;

    private Option<Boolean> isPrimaryAuthorized;

    private Option<Boolean> isSharingInformationWithCoBorrowers;

    private Option<TestMilitaryStatus> militaryStatus;

    private Option<LocalDate> militaryServiceExpectedCompletionDate;

    private Option<TestEmploymentStatus> employmentStatus;

    private Option<TestCreditPullConsentType> softCreditPullConsentType;

    private Option<TestCreditPullStatus> creditPullStatus;

    private Option<TestCreditPullType> creditPullType;

    private Option<List<TestCreditScoreData>> creditScoreData;

    private Option<TestBorrowerWorkflowData> borrowerWorkflowData;

    private Option<Boolean> homeownerPastThreeYears;

    private Option<Boolean> outstandingJudgmentsIndicator;

    private Option<Boolean> presentlyDelinquentIndicator;

    private Option<Boolean> partyToLawsuitIndicator;

    private Option<Boolean> priorPropertyDeedInLieuConveyedIndicator;

    private Option<Boolean> priorPropertyShortSaleCompletedIndicator;

    private Option<Boolean> priorPropertyForeclosureCompletedIndicator;

    private Option<Boolean> bankruptcyIndicator;

    private Option<List<TestBankruptcyData>> bankruptcies;

    TestBorrowerDataBuilder() {
    }

    private TestBorrowerDataBuilder withId(String id) {
      this.id = id;
      return this;
    }

    public TestBorrowerDataBuilder withAction(TestEntityAction action) {
      this.action = action;
      return this;
    }

    public TestBorrowerDataBuilder withIncomeWorkflowData(
        TestIncomeWorkflowData incomeWorkflowData) {
      this.incomeWorkflowData = Option.of(incomeWorkflowData);
      return this;
    }

    public TestBorrowerDataBuilder withBorrowerType(TestBorrowerType borrowerType) {
      this.borrowerType = Option.of(borrowerType);
      return this;
    }

    public TestBorrowerDataBuilder withFirstName(String firstName) {
      this.firstName = Option.of(firstName);
      return this;
    }

    public TestBorrowerDataBuilder withMiddleName(String middleName) {
      this.middleName = Option.of(middleName);
      return this;
    }

    public TestBorrowerDataBuilder withLastName(String lastName) {
      this.lastName = Option.of(lastName);
      return this;
    }

    public TestBorrowerDataBuilder withSuffix(String suffix) {
      this.suffix = Option.of(suffix);
      return this;
    }

    public TestBorrowerDataBuilder withAlternativeNames(List<String> alternativeNames) {
      this.alternativeNames = Option.of(alternativeNames);
      return this;
    }

    public TestBorrowerDataBuilder withTermsOfServiceConsentedAt(
        DateTime termsOfServiceConsentedAt) {
      this.termsOfServiceConsentedAt = Option.of(termsOfServiceConsentedAt);
      return this;
    }

    public TestBorrowerDataBuilder withCreditPullConsentedAt(DateTime creditPullConsentedAt) {
      this.creditPullConsentedAt = Option.of(creditPullConsentedAt);
      return this;
    }

    public TestBorrowerDataBuilder withEmailAddress(EmailAddress emailAddress) {
      this.emailAddress = Option.of(emailAddress);
      return this;
    }

    public TestBorrowerDataBuilder withPhoneNumber(PhoneNumber phoneNumber) {
      this.phoneNumber = Option.of(phoneNumber);
      return this;
    }

    public TestBorrowerDataBuilder withDateOfBirth(LocalDate dateOfBirth) {
      this.dateOfBirth = Option.of(dateOfBirth);
      return this;
    }

    public TestBorrowerDataBuilder withCurrentAddress(TestAddressData currentAddress) {
      this.currentAddress = Option.of(currentAddress);
      return this;
    }

    public TestBorrowerDataBuilder withMailingAddress(TestAddressData mailingAddress) {
      this.mailingAddress = Option.of(mailingAddress);
      return this;
    }

    public TestBorrowerDataBuilder withMaritalStatus(TestMaritalStatus maritalStatus) {
      this.maritalStatus = Option.of(maritalStatus);
      return this;
    }

    public TestBorrowerDataBuilder withCitizenshipType(TestCitizenshipType citizenshipType) {
      this.citizenshipType = Option.of(citizenshipType);
      return this;
    }

    public TestBorrowerDataBuilder withSpouseBorrowerId(String spouseBorrowerId) {
      this.spouseBorrowerId = Option.of(spouseBorrowerId);
      return this;
    }

    public TestBorrowerDataBuilder withIsCurrentlyLivingWithCoBorrowers(
        Boolean isCurrentlyLivingWithCoBorrowers) {
      this.isCurrentlyLivingWithCoBorrowers = Option.of(isCurrentlyLivingWithCoBorrowers);
      return this;
    }

    public TestBorrowerDataBuilder withIsIntendingToOccupy(Boolean isIntendingToOccupy) {
      this.isIntendingToOccupy = Option.of(isIntendingToOccupy);
      return this;
    }

    public TestBorrowerDataBuilder withIsPrimaryAuthorized(Boolean isPrimaryAuthorized) {
      this.isPrimaryAuthorized = Option.of(isPrimaryAuthorized);
      return this;
    }

    public TestBorrowerDataBuilder withIsSharingInformationWithCoBorrowers(
        Boolean isSharingInformationWithCoBorrowers) {
      this.isSharingInformationWithCoBorrowers = Option.of(isSharingInformationWithCoBorrowers);
      return this;
    }

    public TestBorrowerDataBuilder withMilitaryStatus(TestMilitaryStatus militaryStatus) {
      this.militaryStatus = Option.of(militaryStatus);
      return this;
    }

    public TestBorrowerDataBuilder withMilitaryServiceExpectedCompletionDate(
        LocalDate militaryServiceExpectedCompletionDate) {
      this.militaryServiceExpectedCompletionDate = Option.of(militaryServiceExpectedCompletionDate);
      return this;
    }

    public TestBorrowerDataBuilder withEmploymentStatus(TestEmploymentStatus employmentStatus) {
      this.employmentStatus = Option.of(employmentStatus);
      return this;
    }

    public TestBorrowerDataBuilder withSoftCreditPullConsentType(
        TestCreditPullConsentType softCreditPullConsentType) {
      this.softCreditPullConsentType = Option.of(softCreditPullConsentType);
      return this;
    }

    public TestBorrowerDataBuilder withCreditPullStatus(TestCreditPullStatus creditPullStatus) {
      this.creditPullStatus = Option.of(creditPullStatus);
      return this;
    }

    public TestBorrowerDataBuilder withCreditPullType(TestCreditPullType creditPullType) {
      this.creditPullType = Option.of(creditPullType);
      return this;
    }

    public TestBorrowerDataBuilder withCreditScoreData(List<TestCreditScoreData> creditScoreData) {
      this.creditScoreData = Option.of(creditScoreData);
      return this;
    }

    public TestBorrowerDataBuilder withBorrowerWorkflowData(
        TestBorrowerWorkflowData borrowerWorkflowData) {
      this.borrowerWorkflowData = Option.of(borrowerWorkflowData);
      return this;
    }

    public TestBorrowerDataBuilder withHomeownerPastThreeYears(Boolean homeownerPastThreeYears) {
      this.homeownerPastThreeYears = Option.of(homeownerPastThreeYears);
      return this;
    }

    public TestBorrowerDataBuilder withOutstandingJudgmentsIndicator(
        Boolean outstandingJudgmentsIndicator) {
      this.outstandingJudgmentsIndicator = Option.of(outstandingJudgmentsIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPresentlyDelinquentIndicator(
        Boolean presentlyDelinquentIndicator) {
      this.presentlyDelinquentIndicator = Option.of(presentlyDelinquentIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPartyToLawsuitIndicator(Boolean partyToLawsuitIndicator) {
      this.partyToLawsuitIndicator = Option.of(partyToLawsuitIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPriorPropertyDeedInLieuConveyedIndicator(
        Boolean priorPropertyDeedInLieuConveyedIndicator) {
      this.priorPropertyDeedInLieuConveyedIndicator = Option.of(priorPropertyDeedInLieuConveyedIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPriorPropertyShortSaleCompletedIndicator(
        Boolean priorPropertyShortSaleCompletedIndicator) {
      this.priorPropertyShortSaleCompletedIndicator = Option.of(priorPropertyShortSaleCompletedIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPriorPropertyForeclosureCompletedIndicator(
        Boolean priorPropertyForeclosureCompletedIndicator) {
      this.priorPropertyForeclosureCompletedIndicator = Option.of(priorPropertyForeclosureCompletedIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withBankruptcyIndicator(Boolean bankruptcyIndicator) {
      this.bankruptcyIndicator = Option.of(bankruptcyIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withBankruptcies(List<TestBankruptcyData> bankruptcies) {
      this.bankruptcies = Option.of(bankruptcies);
      return this;
    }

    public TestBorrowerData build() {
      TestBorrowerData entity = new TestBorrowerData(this.id, this.incomeWorkflowData == null ? null : this.incomeWorkflowData.getOrNull(), this.borrowerType == null ? null : this.borrowerType.getOrNull(), this.firstName == null ? null : this.firstName.getOrNull(), this.middleName == null ? null : this.middleName.getOrNull(), this.lastName == null ? null : this.lastName.getOrNull(), this.suffix == null ? null : this.suffix.getOrNull(), this.alternativeNames == null ? null : this.alternativeNames.getOrNull(), this.termsOfServiceConsentedAt == null ? null : this.termsOfServiceConsentedAt.getOrNull(), this.creditPullConsentedAt == null ? null : this.creditPullConsentedAt.getOrNull(), this.emailAddress == null ? null : this.emailAddress.getOrNull(), this.phoneNumber == null ? null : this.phoneNumber.getOrNull(), this.dateOfBirth == null ? null : this.dateOfBirth.getOrNull(), this.currentAddress == null ? null : this.currentAddress.getOrNull(), this.mailingAddress == null ? null : this.mailingAddress.getOrNull(), this.maritalStatus == null ? null : this.maritalStatus.getOrNull(), this.citizenshipType == null ? null : this.citizenshipType.getOrNull(), this.spouseBorrowerId == null ? null : this.spouseBorrowerId.getOrNull(), this.isCurrentlyLivingWithCoBorrowers == null ? null : this.isCurrentlyLivingWithCoBorrowers.getOrNull(), this.isIntendingToOccupy == null ? null : this.isIntendingToOccupy.getOrNull(), this.isPrimaryAuthorized == null ? null : this.isPrimaryAuthorized.getOrNull(), this.isSharingInformationWithCoBorrowers == null ? null : this.isSharingInformationWithCoBorrowers.getOrNull(), this.militaryStatus == null ? null : this.militaryStatus.getOrNull(), this.militaryServiceExpectedCompletionDate == null ? null : this.militaryServiceExpectedCompletionDate.getOrNull(), this.employmentStatus == null ? null : this.employmentStatus.getOrNull(), this.softCreditPullConsentType == null ? null : this.softCreditPullConsentType.getOrNull(), this.creditPullStatus == null ? null : this.creditPullStatus.getOrNull(), this.creditPullType == null ? null : this.creditPullType.getOrNull(), this.creditScoreData == null ? null : this.creditScoreData.getOrNull(), this.borrowerWorkflowData == null ? null : this.borrowerWorkflowData.getOrNull(), this.homeownerPastThreeYears == null ? null : this.homeownerPastThreeYears.getOrNull(), this.outstandingJudgmentsIndicator == null ? null : this.outstandingJudgmentsIndicator.getOrNull(), this.presentlyDelinquentIndicator == null ? null : this.presentlyDelinquentIndicator.getOrNull(), this.partyToLawsuitIndicator == null ? null : this.partyToLawsuitIndicator.getOrNull(), this.priorPropertyDeedInLieuConveyedIndicator == null ? null : this.priorPropertyDeedInLieuConveyedIndicator.getOrNull(), this.priorPropertyShortSaleCompletedIndicator == null ? null : this.priorPropertyShortSaleCompletedIndicator.getOrNull(), this.priorPropertyForeclosureCompletedIndicator == null ? null : this.priorPropertyForeclosureCompletedIndicator.getOrNull(), this.bankruptcyIndicator == null ? null : this.bankruptcyIndicator.getOrNull(), this.bankruptcies == null ? null : this.bankruptcies.getOrNull());
      entity.setInternalId(this.id);
      entity.setFieldsToNullSet(getFieldsToNullSet());
      return entity;
    }

    private Set<String> getFieldsToNullSet() {
      Set<String> fieldsToNullSet = new HashSet<>();
      if (this.alternativeNames != null && this.alternativeNames.isEmpty()) { fieldsToNullSet.add("alternativeNames"); };
      if (this.bankruptcies != null && this.bankruptcies.isEmpty()) { fieldsToNullSet.add("bankruptcies"); };
      if (this.bankruptcyIndicator != null && this.bankruptcyIndicator.isEmpty()) { fieldsToNullSet.add("bankruptcyIndicator"); };
      if (this.borrowerType != null && this.borrowerType.isEmpty()) { fieldsToNullSet.add("borrowerType"); };
      if (this.borrowerWorkflowData != null && this.borrowerWorkflowData.isEmpty()) { fieldsToNullSet.add("borrowerWorkflowData"); };
      if (this.citizenshipType != null && this.citizenshipType.isEmpty()) { fieldsToNullSet.add("citizenshipType"); };
      if (this.creditPullConsentedAt != null && this.creditPullConsentedAt.isEmpty()) { fieldsToNullSet.add("creditPullConsentedAt"); };
      if (this.creditPullStatus != null && this.creditPullStatus.isEmpty()) { fieldsToNullSet.add("creditPullStatus"); };
      if (this.creditPullType != null && this.creditPullType.isEmpty()) { fieldsToNullSet.add("creditPullType"); };
      if (this.creditScoreData != null && this.creditScoreData.isEmpty()) { fieldsToNullSet.add("creditScoreData"); };
      if (this.currentAddress != null && this.currentAddress.isEmpty()) { fieldsToNullSet.add("currentAddress"); };
      if (this.dateOfBirth != null && this.dateOfBirth.isEmpty()) { fieldsToNullSet.add("dateOfBirth"); };
      if (this.emailAddress != null && this.emailAddress.isEmpty()) { fieldsToNullSet.add("emailAddress"); };
      if (this.employmentStatus != null && this.employmentStatus.isEmpty()) { fieldsToNullSet.add("employmentStatus"); };
      if (this.firstName != null && this.firstName.isEmpty()) { fieldsToNullSet.add("firstName"); };
      if (this.homeownerPastThreeYears != null && this.homeownerPastThreeYears.isEmpty()) { fieldsToNullSet.add("homeownerPastThreeYears"); };
      if (this.incomeWorkflowData != null && this.incomeWorkflowData.isEmpty()) { fieldsToNullSet.add("incomeWorkflowData"); };
      if (this.isCurrentlyLivingWithCoBorrowers != null && this.isCurrentlyLivingWithCoBorrowers.isEmpty()) { fieldsToNullSet.add("isCurrentlyLivingWithCoBorrowers"); };
      if (this.isIntendingToOccupy != null && this.isIntendingToOccupy.isEmpty()) { fieldsToNullSet.add("isIntendingToOccupy"); };
      if (this.isPrimaryAuthorized != null && this.isPrimaryAuthorized.isEmpty()) { fieldsToNullSet.add("isPrimaryAuthorized"); };
      if (this.isSharingInformationWithCoBorrowers != null && this.isSharingInformationWithCoBorrowers.isEmpty()) { fieldsToNullSet.add("isSharingInformationWithCoBorrowers"); };
      if (this.lastName != null && this.lastName.isEmpty()) { fieldsToNullSet.add("lastName"); };
      if (this.mailingAddress != null && this.mailingAddress.isEmpty()) { fieldsToNullSet.add("mailingAddress"); };
      if (this.maritalStatus != null && this.maritalStatus.isEmpty()) { fieldsToNullSet.add("maritalStatus"); };
      if (this.middleName != null && this.middleName.isEmpty()) { fieldsToNullSet.add("middleName"); };
      if (this.militaryServiceExpectedCompletionDate != null && this.militaryServiceExpectedCompletionDate.isEmpty()) { fieldsToNullSet.add("militaryServiceExpectedCompletionDate"); };
      if (this.militaryStatus != null && this.militaryStatus.isEmpty()) { fieldsToNullSet.add("militaryStatus"); };
      if (this.outstandingJudgmentsIndicator != null && this.outstandingJudgmentsIndicator.isEmpty()) { fieldsToNullSet.add("outstandingJudgmentsIndicator"); };
      if (this.partyToLawsuitIndicator != null && this.partyToLawsuitIndicator.isEmpty()) { fieldsToNullSet.add("partyToLawsuitIndicator"); };
      if (this.phoneNumber != null && this.phoneNumber.isEmpty()) { fieldsToNullSet.add("phoneNumber"); };
      if (this.presentlyDelinquentIndicator != null && this.presentlyDelinquentIndicator.isEmpty()) { fieldsToNullSet.add("presentlyDelinquentIndicator"); };
      if (this.priorPropertyDeedInLieuConveyedIndicator != null && this.priorPropertyDeedInLieuConveyedIndicator.isEmpty()) { fieldsToNullSet.add("priorPropertyDeedInLieuConveyedIndicator"); };
      if (this.priorPropertyForeclosureCompletedIndicator != null && this.priorPropertyForeclosureCompletedIndicator.isEmpty()) { fieldsToNullSet.add("priorPropertyForeclosureCompletedIndicator"); };
      if (this.priorPropertyShortSaleCompletedIndicator != null && this.priorPropertyShortSaleCompletedIndicator.isEmpty()) { fieldsToNullSet.add("priorPropertyShortSaleCompletedIndicator"); };
      if (this.softCreditPullConsentType != null && this.softCreditPullConsentType.isEmpty()) { fieldsToNullSet.add("softCreditPullConsentType"); };
      if (this.spouseBorrowerId != null && this.spouseBorrowerId.isEmpty()) { fieldsToNullSet.add("spouseBorrowerId"); };
      if (this.suffix != null && this.suffix.isEmpty()) { fieldsToNullSet.add("suffix"); };
      if (this.termsOfServiceConsentedAt != null && this.termsOfServiceConsentedAt.isEmpty()) { fieldsToNullSet.add("termsOfServiceConsentedAt"); };
      return fieldsToNullSet;
    }
  }
}
