package com.wealthfront.lending.mortgages.application.model.data.factory;

import com.google.inject.Provider;
import com.kaching.platform.common.Option;
import com.wealthfront.auto.types.global.TestBorrowerWorkflowData;
import com.wealthfront.auto.types.global.TestEntityAction;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.joda.time.DateTime;

public class TestBorrowerWorkflowDataFactory {
  public static TestBorrowerWorkflowDataBuilder createBorrowerWorkflowData(
      Provider<UUID> uuidProvider) {
    TestBorrowerWorkflowDataBuilder builder = new TestBorrowerWorkflowDataBuilder();
    builder.withId(uuidProvider.get().toString());
    builder.withAction(TestEntityAction.ADD);
    return builder;
  }

  public static TestBorrowerWorkflowDataBuilder updateBorrowerWorkflowData(String id) {
    TestBorrowerWorkflowDataBuilder builder = new TestBorrowerWorkflowDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.UPDATE);
    return builder;
  }

  public static TestBorrowerWorkflowData deleteBorrowerWorkflowData(String id) {
    TestBorrowerWorkflowDataBuilder builder = new TestBorrowerWorkflowDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.REMOVE);
    return builder.build();
  }

  public static class TestBorrowerWorkflowDataBuilder {
    private TestEntityAction action;

    private String id;

    private Option<DateTime> createdAt;

    private Option<String> activeCreditPullId;

    TestBorrowerWorkflowDataBuilder() {
    }

    private TestBorrowerWorkflowDataBuilder withId(String id) {
      this.id = id;
      return this;
    }

    public TestBorrowerWorkflowDataBuilder withAction(TestEntityAction action) {
      this.action = action;
      return this;
    }

    public TestBorrowerWorkflowDataBuilder withCreatedAt(DateTime createdAt) {
      this.createdAt = Option.of(createdAt);
      return this;
    }

    public TestBorrowerWorkflowDataBuilder withActiveCreditPullId(String activeCreditPullId) {
      this.activeCreditPullId = Option.of(activeCreditPullId);
      return this;
    }

    public TestBorrowerWorkflowData build() {
      TestBorrowerWorkflowData.Builder entityBuilder = new TestBorrowerWorkflowData.Builder();
      entityBuilder.withId(this.id);
      entityBuilder.withAction(this.action);
      if (this.createdAt != null) { entityBuilder.withCreatedAt(this.createdAt.getOrNull()); };
      if (this.activeCreditPullId != null) { entityBuilder.withActiveCreditPullId(this.activeCreditPullId.getOrNull()); };
      TestBorrowerWorkflowData entity = entityBuilder.build();
      entity.setFieldsToNullSet(getFieldsToNullSet());
      return entity;
    }

    private Set<String> getFieldsToNullSet() {
      Set<String> fieldsToNullSet = new HashSet<>();
      if (this.createdAt != null && this.createdAt.isEmpty()) { fieldsToNullSet.add("createdAt"); };
      if (this.activeCreditPullId != null && this.activeCreditPullId.isEmpty()) { fieldsToNullSet.add("activeCreditPullId"); };
      return fieldsToNullSet;
    }
  }
}
