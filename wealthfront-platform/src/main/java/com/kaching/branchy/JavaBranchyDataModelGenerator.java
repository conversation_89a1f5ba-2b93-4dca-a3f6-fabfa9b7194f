package com.kaching.branchy;

import static com.kaching.api.JavaApiSchemaIntrospector.introspectTypeOrThrow;

import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableList;
import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Stage;
import com.kaching.api.GlobalEntityNamespaceTransformer;
import com.kaching.api.JavaApiCodeGenerator;
import com.kaching.api.JavaApiTypesBuilder;
import com.kaching.api.JavaApiValidator;
import com.kaching.fs.ExposeEntitiesClassLoader;
import com.kaching.platform.common.Errors;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.util.io.UncheckedIo;
import com.squareup.javapoet.JavaFile;

public class JavaBranchyDataModelGenerator {

  @VisibleForTesting
  public FileSystem fs = FileSystems.getDefault();

  private final String buildDirectory;

  public JavaBranchyDataModelGenerator(String buildDirectory) {
    this.buildDirectory = buildDirectory;
  }

  public static void main(String... args) throws IOException {
    Preconditions.checkArgument(args.length == 1, "exactly one argument required (build directory)");
    Preconditions.checkNotNull(args[0], "build directory (1st arg) must be defined");

    JavaBranchyDataModelGenerator generator = new JavaBranchyDataModelGenerator(args[0]);
    Guice.createInjector(Stage.DEVELOPMENT).injectMembers(generator);
    generator.generate();
  }

  @Inject JavaApiCodeGenerator javaApiCodeGenerator;
  @Inject JavaBranchyDataFactoryGenerator javaBranchyDataFactoryGenerator;
  @Inject ExposeEntitiesClassLoader exposeEntitiesClassLoader;
  @Inject GlobalEntityNamespaceTransformer namespaceTransformer;
  @Inject JavaApiValidator javaApiValidator;

  @VisibleForTesting
  List<JavaFile> generate() throws IOException {
    Set<Class<?>> globalClasses = exposeEntitiesClassLoader.getExposedEntities(fs, buildDirectory);
    return generateEntities(globalClasses);
  }

  public List<JavaFile> generateEntity(Class<?> entityClass) {
    return generateEntities(ImmutableList.of(entityClass));
  }

  public List<JavaFile> generateEntities(Collection<Class<?>> entityClasses) {
    JavaApiTypesBuilder typesBuilder = new JavaApiTypesBuilder();
    Errors errors = new Errors();
    for (Class<?> clazz : entityClasses) {
      introspectTypeOrThrow(new JavaApiTypesBuilder.TypeKey(clazz, true), typesBuilder);
    }

    List<JavaFile> files = javaApiCodeGenerator.generateFilesFromJsonEntities(errors, NoOpServiceKind.class, typesBuilder, namespaceTransformer);
    files.addAll(javaBranchyDataFactoryGenerator.generateFactories(entityClasses, typesBuilder, namespaceTransformer));
    errors.throwIfHasErrors();

    for (JavaFile file : files) {
      Path targetDirectory = fs.getPath(GlobalEntityNamespaceTransformer.GENERATED_GLOBAL_ENTITY_PATH_PREFIX);
      UncheckedIo.run(() -> file.writeTo(targetDirectory));
    }

    return files;
  }

  public static class NoOpServiceKind implements ServiceKind {

  }
}
