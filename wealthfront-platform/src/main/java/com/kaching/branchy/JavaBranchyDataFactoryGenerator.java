package com.kaching.branchy;

import static java.util.Collections.emptyList;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import javax.lang.model.element.Modifier;

import org.apache.commons.lang3.StringUtils;

import com.google.inject.Provider;
import com.kaching.api.GlobalEntityNamespaceTransformer;
import com.kaching.api.JavaApiCodeGeneratorImpl;
import com.kaching.api.JavaApiQuerySchema;
import com.kaching.api.JavaApiType.JavaObjectField;
import com.kaching.api.JavaApiType.JavaObjectType;
import com.kaching.api.JavaApiTypeNamespaceTransformer;
import com.kaching.api.JavaApiTypesBuilder;
import com.kaching.api.JavaJsonEntityGenerator;
import com.kaching.annotations.BranchyDataEntity;
import com.kaching.platform.common.Option;
import com.kaching.platform.queryengine.Query;
import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.FieldSpec;
import com.squareup.javapoet.JavaFile;
import com.squareup.javapoet.MethodSpec;
import com.squareup.javapoet.ParameterizedTypeName;
import com.squareup.javapoet.TypeName;
import com.squareup.javapoet.TypeSpec;

public class JavaBranchyDataFactoryGenerator {

  public Collection<JavaFile> generateFactories(
      Collection<Class<?>> entityClasses,
      JavaApiTypesBuilder typesBuilder,
      GlobalEntityNamespaceTransformer namespaceTransformer) {
    List<JavaFile> generatedFiles = new ArrayList<>();

    typesBuilder.getObjectTypes().forEach((entityClass, objectType) -> {
      if (!BranchyDataEntity.class.isAssignableFrom(entityClass)) {
        return;
      }
      generatedFiles.add(generateFactoryForEntity(objectType, namespaceTransformer));
    });

    return generatedFiles;
  }

  private JavaFile generateFactoryForEntity(
      JavaObjectType objectType,
      GlobalEntityNamespaceTransformer namespaceTransformer) {

    JavaApiQuerySchema querySchema = new JavaApiQuerySchema(
        JavaApiCodeGeneratorImpl.GlobalTypeTempQuery.class, emptyList(), objectType);

    JavaApiTypeNamespaceTransformer.FullyQualifiedTypeName globalEntityName =
        namespaceTransformer.resolveObject(
            JavaJsonEntityGenerator.NoOpServiceKind.class, querySchema, objectType);

    String factoryPackageName = "com.wealthfront.lending.mortgages.application.model.data.factory";
    String originalEntitySimpleName = globalEntityName.getSimpleName();
    
    String factorySimpleName = originalEntitySimpleName + "Factory";
    ClassName factoryClassName = ClassName.get(factoryPackageName, factorySimpleName);

    String baseEntitySimpleNameForMethods = originalEntitySimpleName.startsWith("Test") ? 
                              originalEntitySimpleName.substring(4) :
                              originalEntitySimpleName;

    ClassName entityGlobalTypeName = globalEntityName.getJavaPoetClassName();
    String builderSimpleName = originalEntitySimpleName + "Builder"; 
    ClassName builderTypeName = factoryClassName.nestedClass(builderSimpleName);

    ClassName testEntityActionTypeName = ClassName.get("com.wealthfront.auto.types.global", "TestEntityAction");

    TypeSpec.Builder factoryBuilder =
        TypeSpec.classBuilder(factoryClassName).addModifiers(Modifier.PUBLIC);

    factoryBuilder.addMethod(MethodSpec.methodBuilder("create" + baseEntitySimpleNameForMethods)
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .returns(builderTypeName)
        .addParameter(ParameterizedTypeName.get(ClassName.get(Provider.class), ClassName.get(UUID.class)), "uuidProvider")
        .addStatement("$T builder = new $T()", builderTypeName, builderTypeName)
        .addStatement("builder.withId(uuidProvider.get().toString())")
        .addStatement("builder.withAction($T.ADD)", testEntityActionTypeName)
        .addStatement("return builder")
        .build());

    factoryBuilder.addMethod(MethodSpec.methodBuilder("update" + baseEntitySimpleNameForMethods)
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .returns(builderTypeName)
        .addParameter(String.class, "id")
        .addStatement("$T builder = new $T()", builderTypeName, builderTypeName)
        .addStatement("builder.withId(id)")
        .addStatement("builder.withAction($T.UPDATE)", testEntityActionTypeName)
        .addStatement("return builder")
        .build());

    factoryBuilder.addMethod(MethodSpec.methodBuilder("delete" + baseEntitySimpleNameForMethods)
        .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
        .returns(entityGlobalTypeName)
        .addParameter(String.class, "id")
        .addStatement("$T builder = new $T()", builderTypeName, builderTypeName)
        .addStatement("builder.withId(id)")
        .addStatement("builder.withAction($T.REMOVE)", testEntityActionTypeName)
        .addStatement("return builder.build()")
        .build());

    TypeSpec.Builder nestedBuilder =
        TypeSpec.classBuilder(builderSimpleName).addModifiers(Modifier.PUBLIC, Modifier.STATIC);

    List<JavaObjectField> allFields = JavaApiCodeGeneratorImpl.getAllTypeFields(objectType);
    List<JavaObjectField> settableFieldsInBuilder = new ArrayList<>();

    nestedBuilder.addField(testEntityActionTypeName, "action", Modifier.PRIVATE); 
    nestedBuilder.addField(String.class, "id", Modifier.PRIVATE);

    for (JavaObjectField field : allFields) {
      if (!field.getJavaName().equals("id") && !field.getJavaName().equals("action")) {
        settableFieldsInBuilder.add(field);
        TypeName rewrittenFieldType = JavaApiCodeGeneratorImpl.rewriteJavaType(
            field.getApiType(),
            JavaJsonEntityGenerator.NoOpServiceKind.class,
            querySchema,
            namespaceTransformer);
        TypeName fieldTypeInBuilder = ParameterizedTypeName.get(ClassName.get(Option.class), rewrittenFieldType.box());
        nestedBuilder.addField(FieldSpec.builder(fieldTypeInBuilder, field.getJavaName(), Modifier.PRIVATE).build());
      }
    }
    
    nestedBuilder.addMethod(MethodSpec.methodBuilder("withId")
        .addModifiers(Modifier.PRIVATE)
        .returns(builderTypeName)
        .addParameter(String.class, "id")
        .addStatement("this.id = id")
        .addStatement("return this")
        .build());

    nestedBuilder.addMethod(MethodSpec.methodBuilder("withAction")
        .addModifiers(Modifier.PUBLIC) 
        .returns(builderTypeName)
        .addParameter(testEntityActionTypeName, "action")
        .addStatement("this.action = action")
        .addStatement("return this")
        .build());

    for (JavaObjectField field : settableFieldsInBuilder) {
        TypeName rewrittenFieldType = JavaApiCodeGeneratorImpl.rewriteJavaType(
            field.getApiType(),
            JavaJsonEntityGenerator.NoOpServiceKind.class,
            querySchema,
            namespaceTransformer);
        MethodSpec.Builder setterMethod =
            MethodSpec.methodBuilder("with" + StringUtils.capitalize(field.getJavaName()))
                .addModifiers(Modifier.PUBLIC)
                .returns(builderTypeName)
                .addParameter(rewrittenFieldType.box(), field.getJavaName());
        setterMethod.addStatement("this.$N = $T.of($N)", field.getJavaName(), Option.class, field.getJavaName());
        setterMethod.addStatement("return this");
        nestedBuilder.addMethod(setterMethod.build());
    }
    
    MethodSpec.Builder buildMethod = MethodSpec.methodBuilder("build")
        .addModifiers(Modifier.PUBLIC)
        .returns(entityGlobalTypeName);

    buildMethod.addStatement("$T.Builder entityBuilder = new $T.Builder()", entityGlobalTypeName, entityGlobalTypeName);
    buildMethod.addStatement("entityBuilder.withId(this.id)");
    buildMethod.addStatement("entityBuilder.withAction(this.action)");

    for (JavaObjectField field : settableFieldsInBuilder) {
        buildMethod.addStatement(
            "if (this.$N != null) { entityBuilder.with$L(this.$N.getOrNull()); }",
            field.getJavaName(),
            StringUtils.capitalize(field.getJavaName()),
            field.getJavaName());
    }

    buildMethod.addStatement("$T entity = entityBuilder.build()", entityGlobalTypeName);
    buildMethod.addStatement("entity.setFieldsToNullSet(getFieldsToNullSet())");
    buildMethod.addStatement("return entity");
    nestedBuilder.addMethod(buildMethod.build());

    MethodSpec.Builder getFieldsToNullSetMethod =
        MethodSpec.methodBuilder("getFieldsToNullSet")
            .addModifiers(Modifier.PRIVATE)
            .returns(ParameterizedTypeName.get(ClassName.get(Set.class), ClassName.get(String.class)));
    getFieldsToNullSetMethod.addStatement("$T<$T> fieldsToNullSet = new $T<>()", Set.class, String.class, HashSet.class);

    for (JavaObjectField field : settableFieldsInBuilder) {
        getFieldsToNullSetMethod.addStatement(
            "if (this.$N != null && this.$N.isEmpty()) { fieldsToNullSet.add($S); }",
            field.getJavaName(),
            field.getJavaName(),
            field.getJavaName());
    }
    getFieldsToNullSetMethod.addStatement("return fieldsToNullSet");
    nestedBuilder.addMethod(getFieldsToNullSetMethod.build());

    nestedBuilder.addMethod(MethodSpec.constructorBuilder().build());

    factoryBuilder.addType(nestedBuilder.build());

    return JavaFile.builder(factoryPackageName, factoryBuilder.build())
        .skipJavaLangImports(true)
        .indent("  ")
        .build();
  }
}
