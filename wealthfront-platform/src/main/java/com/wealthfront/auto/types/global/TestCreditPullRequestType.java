package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCreditPullRequestType {
  INDIVIDUAL,

  JOINT;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case INDIVIDUAL:
        return visitor.caseIndividual();
      case JOINT:
        return visitor.caseJoint();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseIndividual();

    T caseJoint();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseIndividual() {
      return defaultValue.get();
    }

    @Override
    public T caseJoint() {
      return defaultValue.get();
    }
  }
}
