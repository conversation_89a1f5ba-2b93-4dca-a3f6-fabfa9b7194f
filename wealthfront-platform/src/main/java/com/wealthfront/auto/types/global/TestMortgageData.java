package com.wealthfront.auto.types.global;

import com.kaching.annotations.BranchyDataEntity;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.SERVICE,
    value = ExposeTo.BACKEND
)
@Entity(
    discriminatorName = "type",
    subclasses = {TestAddressData.class, TestApplicationData.class, TestBankruptcyData.class, TestBorrowerData.class, TestBorrowerWorkflowData.class, TestCreditPullData.class, TestCreditScoreData.class, TestIncomeWorkflowData.class, TestLoanAmountData.class}
)
public abstract class TestMortgageData implements BranchyDataEntity {
  public TestMortgageData() {
  }

  public abstract <T> T visit(Visitor<T> visitor);

  public void validate() {
  }

  @Override
  public int hashCode() {
    return 0;
  }

  @Override
  public boolean equals(Object o) {
    return o instanceof TestMortgageData;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestMortgageData.class);
    if (isExactClass) {
      sb.append("TestMortgageData {\n");
    }
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public interface Visitor<T> {
    T caseTestAddressData(TestAddressData testAddressData);

    T caseTestApplicationData(TestApplicationData testApplicationData);

    T caseTestBankruptcyData(TestBankruptcyData testBankruptcyData);

    T caseTestBorrowerData(TestBorrowerData testBorrowerData);

    T caseTestBorrowerWorkflowData(TestBorrowerWorkflowData testBorrowerWorkflowData);

    T caseTestCreditPullData(TestCreditPullData testCreditPullData);

    T caseTestCreditScoreData(TestCreditScoreData testCreditScoreData);

    T caseTestIncomeWorkflowData(TestIncomeWorkflowData testIncomeWorkflowData);

    T caseTestLoanAmountData(TestLoanAmountData testLoanAmountData);
  }

  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseTestAddressData(TestAddressData testAddressData) {
      return defaultValue.get();
    }

    @Override
    public T caseTestApplicationData(TestApplicationData testApplicationData) {
      return defaultValue.get();
    }

    @Override
    public T caseTestBankruptcyData(TestBankruptcyData testBankruptcyData) {
      return defaultValue.get();
    }

    @Override
    public T caseTestBorrowerData(TestBorrowerData testBorrowerData) {
      return defaultValue.get();
    }

    @Override
    public T caseTestBorrowerWorkflowData(TestBorrowerWorkflowData testBorrowerWorkflowData) {
      return defaultValue.get();
    }

    @Override
    public T caseTestCreditPullData(TestCreditPullData testCreditPullData) {
      return defaultValue.get();
    }

    @Override
    public T caseTestCreditScoreData(TestCreditScoreData testCreditScoreData) {
      return defaultValue.get();
    }

    @Override
    public T caseTestIncomeWorkflowData(TestIncomeWorkflowData testIncomeWorkflowData) {
      return defaultValue.get();
    }

    @Override
    public T caseTestLoanAmountData(TestLoanAmountData testLoanAmountData) {
      return defaultValue.get();
    }
  }
}
