package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestMortgageType {
  PURCHASE,

  REFINANCE,

  HELOC;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case PURCHASE:
        return visitor.casePurchase();
      case REFINANCE:
        return visitor.caseRefinance();
      case HELOC:
        return visitor.caseHeloc();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T casePurchase();

    T caseRefinance();

    T caseHeloc();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T casePurchase() {
      return defaultValue.get();
    }

    @Override
    public T caseRefinance() {
      return defaultValue.get();
    }

    @Override
    public T caseHeloc() {
      return defaultValue.get();
    }
  }
}
