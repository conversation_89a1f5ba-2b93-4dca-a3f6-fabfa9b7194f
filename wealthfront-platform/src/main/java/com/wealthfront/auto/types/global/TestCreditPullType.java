package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCreditPullType {
  HARD,

  SOFT;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case HARD:
        return visitor.caseHard();
      case SOFT:
        return visitor.caseSoft();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseHard();

    T caseSoft();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseHard() {
      return defaultValue.get();
    }

    @Override
    public T caseSoft() {
      return defaultValue.get();
    }
  }
}
