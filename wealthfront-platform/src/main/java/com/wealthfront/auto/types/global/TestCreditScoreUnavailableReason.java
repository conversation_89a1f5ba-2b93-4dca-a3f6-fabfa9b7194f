package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCreditScoreUnavailableReason {
  INSUFFICIENT_CREDIT_HISTORY,

  FROZEN;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case INSUFFICIENT_CREDIT_HISTORY:
        return visitor.caseInsufficientCreditHistory();
      case FROZEN:
        return visitor.caseFrozen();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseInsufficientCreditHistory();

    T caseFrozen();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseInsufficientCreditHistory() {
      return defaultValue.get();
    }

    @Override
    public T caseFrozen() {
      return defaultValue.get();
    }
  }
}
